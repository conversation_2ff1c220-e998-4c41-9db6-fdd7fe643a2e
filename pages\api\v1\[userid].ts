import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, RowDataPacket } from 'mysql2/promise';

interface ConsultantDetails {
  id: string;
  name: string;
  email: string;
  pfp: string;
  per_minute_rate: number;
}

interface RequestDetails {
  status: string;
  chatRequestId: string;
  time: string;
  bel: string;
  roomN: string;
}

interface ResponseData {
  userId: string;
  consultantDetails: ConsultantDetails;
  userDetails: {
    name: string;
    email: string;
    pfp: string;
  };
  requestDetails: RequestDetails;
}

interface ChatRequest extends RowDataPacket {
  userId: string;
  status: string;
  userName: string;
  userEmail: string;
  userPfp: string;
  time: string;
  bel: string;
  roomN: string;
  id: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { userid } = req.query;

  if (!userid || Array.isArray(userid)) {
    return res.status(400).json({ error: 'User ID is required and should be a string.' });
  }

  let connection;

  try {
    connection = await connectToDatabase();

    if (req.method === 'GET') {
      const [consultantRows]: [RowDataPacket[], FieldPacket[]] = await connection.query(
        'SELECT id, name, email, pfp, per_minute_rate FROM consultants WHERE id = ?',
        [userid]
      );

      if (consultantRows.length === 0) {
        return res.status(404).json({ message: 'Consultant not found' });
      }

      const consultant = consultantRows[0];
const [chatRequests]: [ChatRequest[], FieldPacket[]] = await connection.query(
  `SELECT cr.id, cr.userId, cr.status, cr.time, cr.bel, cr.roomN,
          TIMESTAMPDIFF(MINUTE, cr.created_at, NOW()) AS age_minutes,
          u.username AS userName, u.email AS userEmail, u.pfp AS userPfp
   FROM chatRequests cr
   JOIN users u ON cr.userId = u.id
   WHERE cr.consultantId = ? AND cr.status != ? AND cr.created_at >= NOW() - INTERVAL 2 HOUR`,
  [userid, 'approved']
);


      if (chatRequests.length === 0) {
        return res.status(200).json([]);
      }

      const detailedRequests: ResponseData[] = chatRequests.map(row => ({
        userId: row.userId,
        consultantDetails: {
          id: consultant.id,
          name: consultant.name,
          email: consultant.email,
          pfp: consultant.pfp,
          per_minute_rate: consultant.per_minute_rate,
        },
        userDetails: {
          name: row.userName,
          email: row.userEmail,
          pfp: row.userPfp,
        },
        requestDetails: {
          status: row.status,
          chatRequestId: row.id,
          time: row.time,
          bel: row.bel,
          roomN: row.roomN,
        }
      }));

      return res.status(200).json(detailedRequests);
    }

    if (req.method === 'POST') {
      const { chatRequestId } = req.body;

      if (!chatRequestId) {
        return res.status(400).json({ error: 'Chat Request ID is required' });
      }

      const [existingChatRequest]: [RowDataPacket[], FieldPacket[]] = await connection.query(
        'SELECT * FROM chatRequests WHERE id = ? AND consultantId = ?',
        [chatRequestId, userid]
      );

      if (existingChatRequest.length === 0) {
        return res.status(404).json({ error: 'Chat request not found or does not belong to this consultant' });
      }

      await connection.query(
        'UPDATE chatRequests SET status = ? WHERE id = ?',
        ['approved', chatRequestId]
      );

      return res.status(200).json({ message: 'Chat request approved successfully' });
    }

    return res.status(405).json({ error: 'Method Not Allowed' });
  } catch (error) {
    console.error('Database error:', error);
    return res.status(500).json({ error: 'Internal Server Error' });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
