{"name": "nityasha_web_api", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build && prisma generate", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/google": "^2.0.8", "@ai-sdk/react": "^2.0.22", "@ai-sdk/ui-utils": "^1.2.11", "@prisma/client": "^5.22.0", "@sparticuz/chromium-min": "^138.0.2", "agora-rtc-sdk-ng": "^4.22.1", "ai": "^5.0.22", "axios": "^1.11.0", "bcrypt": "^5.1.1", "crawler": "^2.0.2", "dotenv": "^16.4.5", "firebase-admin": "^13.0.1", "formidable": "^3.5.1", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "meilisearch": "^0.42.0", "micro": "^10.0.1", "mongoose": "^8.14.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.3", "next": "^15.0.3", "next-auth": "^4.24.8", "next-connect": "^0.12.2", "nodemailer": "^6.9.15", "prisma": "^5.22.0", "puppeteer-core": "^24.17.0", "pusher": "^5.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "redis": "^5.8.2", "stripe": "^16.12.0", "ua-parser-js": "^2.0.0", "ws": "^8.18.0", "zod": "^3.25.76"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/formidable": "^3.4.5", "@types/multer": "^1.4.12", "@types/node": "^20", "@types/nodemailer": "^6.4.16", "@types/react": "^18.3.10", "@types/react-dom": "^18", "@types/ws": "^8.5.13", "eslint": "^8", "eslint-config-next": "^15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}