// app/api/chat/route.ts
import { NextRequest } from 'next/server';
import { google } from '@ai-sdk/google';
import { streamText, tool } from 'ai';
import { z } from 'zod';
import { createClient } from 'redis';
import { query } from '@/lib/db';

export const maxDuration = 60;
export const dynamic = 'force-dynamic';

let redis: any = null;

// Redis connection
async function getRedisClient() {
    if (!redis) {
        redis = createClient({
            socket: {
                host: 'mercury.nityasha.com',
                port: 26739,
            },
            password: 'Amber@!23',
        });

        redis.on('error', (err: unknown) => console.error('Redis error', err));
        await redis.connect();
    }
    return redis;
}

function historyKey(id: string) {
    return `chat:${id}:messages`;
}

// Get current IST time
function getCurrentISTTime() {
    return new Date().toLocaleString('en-IN', {
        timeZone: 'Asia/Kolkata',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    }).replace(/(\d{2})\/(\d{2})\/(\d{4}), (\d{2}):(\d{2}):(\d{2})/, '$3-$2-$1 $4:$5:$6');
}

// Check for pending reminders
async function checkPendingReminders(userId: number) {
    try {
        const currentTime = getCurrentISTTime();
        const reminders = await query(
            `SELECT * FROM reminders 
       WHERE user_id = ? AND is_completed = 0 AND reminder_date <= ? 
       ORDER BY reminder_date ASC`,
            [userId, currentTime]
        ) as any[];

        return reminders;
    } catch (error) {
        console.error('Error checking pending reminders:', error);
        return [];
    }
}

// Google Search function
async function performGoogleSearch(searchQuery: string) {
    try {
        const apiKey = process.env.GOOGLE_SEARCH_API_KEY;
        const searchEngineId = process.env.GOOGLE_SEARCH_ENGINE_ID;

        if (!apiKey || !searchEngineId) {
            return { error: 'Google Search API not configured' };
        }

        const response = await fetch(
            `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=${encodeURIComponent(searchQuery)}&num=5`
        );

        if (!response.ok) {
            throw new Error(`Search API error: ${response.status}`);
        }

        const data = await response.json();

        return {
            query: searchQuery,
            results: data.items?.slice(0, 5).map((item: any) => ({
                title: item.title,
                snippet: item.snippet,
                link: item.link
            })) || []
        };
    } catch (error) {
        console.error('Google search error:', error);
        return { error: 'Search temporarily unavailable' };
    }
}

// Normalize messages for Redis storage and AI SDK
function normalizeMessage(message: any) {
    if (!message || !message.role) {
        return null;
    }

    // Ensure message has proper structure
    if (typeof message.content === 'string') {
        return {
            id: message.id || crypto.randomUUID(),
            role: message.role,
            content: message.content,
        };
    } else if (Array.isArray(message.content)) {
        // Handle array content (from UI components)
        const textContent = message.content
            .filter((item: any) => item && item.type === 'text')
            .map((item: any) => item.text)
            .join(' ');

        return {
            id: message.id || crypto.randomUUID(),
            role: message.role,
            content: textContent || 'Empty message',
        };
    } else if (message.parts && Array.isArray(message.parts)) {
        // Handle parts format
        const textContent = message.parts
            .filter((part: any) => part && part.type === 'text')
            .map((part: any) => part.text)
            .join(' ');

        return {
            id: message.id || crypto.randomUUID(),
            role: message.role,
            content: textContent || 'Empty message',
        };
    }

    // Fallback
    return {
        id: message.id || crypto.randomUUID(),
        role: message.role,
        content: String(message.content || message.parts || ''),
    };
}

async function loadHistory(id: string) {
    try {
        const client = await getRedisClient();
        const raw = await client.get(historyKey(id));
        const parsed = raw ? JSON.parse(raw) : [];

        // Ensure parsed is an array and normalize each message
        if (!Array.isArray(parsed)) {
            console.warn('Invalid history format, returning empty array');
            return [];
        }

        return parsed.filter(msg => msg).map(normalizeMessage).filter(msg => msg !== null);
    } catch (error) {
        console.error('Failed to load history:', error);
        return [];
    }
}

async function saveHistory(id: string, messages: any[]) {
    try {
        const client = await getRedisClient();
        // Normalize messages before saving
        const normalized = messages.map(normalizeMessage);
        await client.set(historyKey(id), JSON.stringify(normalized));
    } catch (error) {
        console.error('Failed to save history:', error);
    }
}

async function clearHistory(id: string) {
    try {
        const client = await getRedisClient();
        await client.del(historyKey(id));
    } catch (error) {
        console.error('Failed to clear history:', error);
    }
}

// Create tools with automatic user_id injection
function createToolsWithUserId(currentUserId: number) {

    // Weather tool
    const weatherTool = tool({
        description: 'Get current weather for a location',
        parameters: z.object({
            location: z.string().describe('City and region/country, e.g. "Guna, Madhya Pradesh, India"'),
            unit: z.enum(['celsius', 'fahrenheit']).optional(),
        }),
        execute: async ({ location, unit = 'celsius' }) => {
            const temperature = unit === 'fahrenheit' ? 86 : 30;
            return { location, unit, temperature, conditions: 'Sunny' };
        },
    });

    // Google Search tool
    const googleSearchTool = tool({
        description: 'Search Google for information on any topic',
        parameters: z.object({
            query: z.string().describe('Search query to look up information'),
        }),
        execute: async ({ query }) => {
            return await performGoogleSearch(query);
        },
    });

    // Check pending reminders tool
    const checkRemindersTool = tool({
        description: 'Check for pending reminders that need user attention',
        parameters: z.object({}),
        execute: async () => {
            try {
                const pendingReminders = await checkPendingReminders(currentUserId);
                return {
                    success: true,
                    pendingCount: pendingReminders.length,
                    reminders: pendingReminders
                };
            } catch (error) {
                console.error('Check reminders error:', error);
                return { success: false, message: 'Failed to check reminders' };
            }
        },
    });

    // Add Reminder tool
    const addReminderTool = tool({
        description: 'Add a new reminder for the user',
        parameters: z.object({
            title: z.string().describe('Reminder title'),
            description: z.string().optional().describe('Reminder description'),
            reminder_date: z.string().describe('Reminder date and time in YYYY-MM-DD HH:MM:SS format (IST)'),
        }),
        execute: async ({ title, description, reminder_date }) => {
            try {
                const result = await query(
                    'INSERT INTO reminders (user_id, title, description, reminder_date) VALUES (?, ?, ?, ?)',
                    [currentUserId, title, description || '', reminder_date]
                ) as any;
                return { success: true, id: result.insertId, message: 'Reminder added successfully' };
            } catch (error) {
                console.error('Add reminder error:', error);
                return { success: false, message: 'Failed to add reminder' };
            }
        },
    });

    // Get Reminders tool
    const getRemindersTool = tool({
        description: 'Get all reminders for the user',
        parameters: z.object({
            completed: z.boolean().optional().describe('Filter by completion status'),
            upcoming_only: z.boolean().optional().describe('Show only upcoming reminders'),
        }),
        execute: async ({ completed, upcoming_only }) => {
            try {
                let sql = 'SELECT * FROM reminders WHERE user_id = ?';
                const params: any[] = [currentUserId];

                if (completed !== undefined) {
                    sql += ' AND is_completed = ?';
                    params.push(completed);
                }

                if (upcoming_only) {
                    sql += ' AND reminder_date > ?';
                    params.push(getCurrentISTTime());
                }

                sql += ' ORDER BY reminder_date ASC';

                const reminders = await query(sql, params);
                return { success: true, reminders, currentTime: getCurrentISTTime() };
            } catch (error) {
                console.error('Get reminders error:', error);
                return { success: false, message: 'Failed to get reminders' };
            }
        },
    });

    // Update Reminder tool
    const updateReminderTool = tool({
        description: 'Update a reminder',
        parameters: z.object({
            reminder_id: z.number().describe('Reminder ID to update'),
            title: z.string().optional().describe('New title'),
            description: z.string().optional().describe('New description'),
            reminder_date: z.string().optional().describe('New reminder date in YYYY-MM-DD HH:MM:SS format (IST)'),
            is_completed: z.boolean().optional().describe('Mark as completed/incomplete'),
        }),
        execute: async ({ reminder_id, title, description, reminder_date, is_completed }) => {
            try {
                const updates: string[] = [];
                const params: any[] = [];

                if (title) { updates.push('title = ?'); params.push(title); }
                if (description !== undefined) { updates.push('description = ?'); params.push(description); }
                if (reminder_date) { updates.push('reminder_date = ?'); params.push(reminder_date); }
                if (is_completed !== undefined) { updates.push('is_completed = ?'); params.push(is_completed); }

                if (updates.length === 0) {
                    return { success: false, message: 'No fields to update' };
                }

                params.push(reminder_id, currentUserId);

                await query(
                    `UPDATE reminders SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`,
                    params
                );

                return { success: true, message: 'Reminder updated successfully' };
            } catch (error) {
                console.error('Update reminder error:', error);
                return { success: false, message: 'Failed to update reminder' };
            }
        },
    });

    // Delete Reminder tool
    const deleteReminderTool = tool({
        description: 'Delete a reminder',
        parameters: z.object({
            reminder_id: z.number().describe('Reminder ID to delete'),
        }),
        execute: async ({ reminder_id }) => {
            try {
                await query('DELETE FROM reminders WHERE id = ? AND user_id = ?', [reminder_id, currentUserId]);
                return { success: true, message: 'Reminder deleted successfully' };
            } catch (error) {
                console.error('Delete reminder error:', error);
                return { success: false, message: 'Failed to delete reminder' };
            }
        },
    });

    // Add Todo List tool
    const addTodoListTool = tool({
        description: 'Create a new todo list for the user',
        parameters: z.object({
            name: z.string().describe('Todo list name'),
            description: z.string().optional().describe('Todo list description'),
        }),
        execute: async ({ name, description }) => {
            try {
                const result = await query(
                    'INSERT INTO todo_lists (user_id, name, description) VALUES (?, ?, ?)',
                    [currentUserId, name, description || '']
                ) as any;
                return { success: true, id: result.insertId, message: 'Todo list created successfully' };
            } catch (error) {
                console.error('Add todo list error:', error);
                return { success: false, message: 'Failed to create todo list' };
            }
        },
    });

    // Get Todo Lists tool
    const getTodoListsTool = tool({
        description: 'Get all todo lists for the user',
        parameters: z.object({}),
        execute: async () => {
            try {
                const lists = await query('SELECT * FROM todo_lists WHERE user_id = ? ORDER BY created_at DESC', [currentUserId]);
                return { success: true, lists };
            } catch (error) {
                console.error('Get todo lists error:', error);
                return { success: false, message: 'Failed to get todo lists' };
            }
        },
    });

    // Add Todo Item tool
    const addTodoItemTool = tool({
        description: 'Add a new todo item to a list',
        parameters: z.object({
            list_id: z.number().optional().describe('Todo list ID (optional, can be null for general items)'),
            title: z.string().describe('Todo item title'),
            description: z.string().optional().describe('Todo item description'),
            priority: z.enum(['low', 'medium', 'high']).optional().describe('Priority level'),
            due_date: z.string().optional().describe('Due date in YYYY-MM-DD HH:MM:SS format (IST)'),
        }),
        execute: async ({ list_id, title, description, priority, due_date }) => {
            try {
                const result = await query(
                    'INSERT INTO todo_items (user_id, list_id, title, description, priority, due_date) VALUES (?, ?, ?, ?, ?, ?)',
                    [currentUserId, list_id || null, title, description || '', priority || 'medium', due_date || null]
                ) as any;
                return { success: true, id: result.insertId, message: 'Todo item added successfully' };
            } catch (error) {
                console.error('Add todo item error:', error);
                return { success: false, message: 'Failed to add todo item' };
            }
        },
    });

    // Get Todo Items tool
    const getTodoItemsTool = tool({
        description: 'Get todo items for the user, optionally filtered by list or completion status',
        parameters: z.object({
            list_id: z.number().optional().describe('Filter by specific todo list ID'),
            completed: z.boolean().optional().describe('Filter by completion status'),
        }),
        execute: async ({ list_id, completed }) => {
            try {
                let sql = `
          SELECT ti.*, tl.name as list_name
          FROM todo_items ti
          LEFT JOIN todo_lists tl ON ti.list_id = tl.id
          WHERE ti.user_id = ?
        `;
                const params: any[] = [currentUserId];

                if (list_id !== undefined) {
                    sql += ' AND ti.list_id = ?';
                    params.push(list_id);
                }

                if (completed !== undefined) {
                    sql += ' AND ti.is_completed = ?';
                    params.push(completed);
                }

                sql += ' ORDER BY ti.due_date ASC, ti.priority DESC, ti.created_at DESC';

                const items = await query(sql, params);
                return { success: true, items };
            } catch (error) {
                console.error('Get todo items error:', error);
                return { success: false, message: 'Failed to get todo items' };
            }
        },
    });

    // Update Todo Item tool
    const updateTodoItemTool = tool({
        description: 'Update a todo item',
        parameters: z.object({
            item_id: z.number().describe('Todo item ID to update'),
            title: z.string().optional().describe('New title'),
            description: z.string().optional().describe('New description'),
            priority: z.enum(['low', 'medium', 'high']).optional().describe('New priority'),
            due_date: z.string().optional().describe('New due date in YYYY-MM-DD HH:MM:SS format (IST)'),
            is_completed: z.boolean().optional().describe('Mark as completed/incomplete'),
        }),
        execute: async ({ item_id, title, description, priority, due_date, is_completed }) => {
            try {
                const updates: string[] = [];
                const params: any[] = [];

                if (title) { updates.push('title = ?'); params.push(title); }
                if (description !== undefined) { updates.push('description = ?'); params.push(description); }
                if (priority) { updates.push('priority = ?'); params.push(priority); }
                if (due_date !== undefined) { updates.push('due_date = ?'); params.push(due_date || null); }
                if (is_completed !== undefined) { updates.push('is_completed = ?'); params.push(is_completed); }

                if (updates.length === 0) {
                    return { success: false, message: 'No fields to update' };
                }

                params.push(item_id, currentUserId);

                await query(
                    `UPDATE todo_items SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`,
                    params
                );

                return { success: true, message: 'Todo item updated successfully' };
            } catch (error) {
                console.error('Update todo item error:', error);
                return { success: false, message: 'Failed to update todo item' };
            }
        },
    });

    // Delete Todo Item tool
    const deleteTodoItemTool = tool({
        description: 'Delete a todo item',
        parameters: z.object({
            item_id: z.number().describe('Todo item ID to delete'),
        }),
        execute: async ({ item_id }) => {
            try {
                await query('DELETE FROM todo_items WHERE id = ? AND user_id = ?', [item_id, currentUserId]);
                return { success: true, message: 'Todo item deleted successfully' };
            } catch (error) {
                console.error('Delete todo item error:', error);
                return { success: false, message: 'Failed to delete todo item' };
            }
        },
    });

    return {
        get_current_weather: weatherTool,
        google_search: googleSearchTool,
        check_pending_reminders: checkRemindersTool,
        add_reminder: addReminderTool,
        get_reminders: getRemindersTool,
        update_reminder: updateReminderTool,
        delete_reminder: deleteReminderTool,
        add_todo_list: addTodoListTool,
        get_todo_lists: getTodoListsTool,
        add_todo_item: addTodoItemTool,
        get_todo_items: getTodoItemsTool,
        update_todo_item: updateTodoItemTool,
        delete_todo_item: deleteTodoItemTool,
    };
}

export async function POST(req: NextRequest) {
    try {
        const body = await req.json();
        const { user_id, message }: { user_id: number; message: string } = body || {};

        if (!user_id || !message) {
            return new Response(
                JSON.stringify({ error: 'Missing required fields: user_id and message' }),
                {
                    status: 400,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // Load chat history
        const id = `chat_${user_id}`;
        const prior = await loadHistory(id);

        // Create new message in proper format
        const newMessage = {
            id: crypto.randomUUID(),
            role: 'user' as const,
            content: message,
        };

        // Combine with history
        let allMessages = [...(prior || []), newMessage];

        // Check for corrupted data and clear if needed
        const hasCorruptedData = (prior || []).some((msg: any) =>
            msg &&
            msg.role === 'assistant' &&
            msg.content &&
            typeof msg.content === 'string' &&
            msg.content.includes('[object Object]')
        );

        if (hasCorruptedData) {
            console.log('Corrupted data detected, clearing history');
            await clearHistory(id);
            allMessages = [newMessage];
        }

        // Ensure all messages have proper format
        const validMessages = (allMessages || [])
            .filter(msg => msg && msg.role && msg.content)
            .map(msg => normalizeMessage(msg))
            .filter(msg => msg !== null);

        console.log('Valid messages:', validMessages.length);

        // Create tools with user ID
        const toolsWithUserId = createToolsWithUserId(user_id);

        // Check for pending reminders automatically
        const pendingReminders = await checkPendingReminders(user_id);
        let reminderContext = '';

        if (pendingReminders.length > 0) {
            reminderContext = `\nनित्यशा, यहाँ user के ${pendingReminders.length} pending reminders हैं:
${pendingReminders.map(r => `- ${r.title} (${r.reminder_date} को)`).join('\n')}

कृपया user को इन reminders के बारे में बताएं।`;
        }

        // System prompt
        const systemPrompt = `You are Nityasha, a helpful personal assistant with a warm and friendly female personality created by Nityasha Team. You can hear and speak. You are chatting with a user over voice.

## Task
Your Task is to assist the user in the most helpful and efficient manner possible. Use the google_search tool to search the internet whenever a user requests recent or external information.
If the user asks a follow-up that might also require fresh details, perform another search instead of assuming previous results, Always verify with a new search to ensure accuracy if there's any uncertainty.
You are chatting via the Nityasha App. This means that your response should be concise and to the point, unless the user's request requires reasoning or long-form outputs.

IMPORTANT: After using any tool, you MUST provide a natural language response to the user explaining what you found or did. Never leave the response empty after using tools.

## Current date and time
${getCurrentISTTime()} (IST - Indian Standard Time)

${reminderContext}

## Available Tools:
- get_current_weather: Get weather information for any location
- google_search: Search Google for current information
- check_pending_reminders: Check for pending reminders
- add_reminder: Add new reminders with date/time
- get_reminders: Get all user's reminders
- update_reminder: Update existing reminders
- delete_reminder: Delete reminders
- add_todo_list: Create new todo lists
- get_todo_lists: Get all todo lists
- add_todo_item: Add items to todo lists
- get_todo_items: Get todo items
- update_todo_item: Update todo items
- delete_todo_item: Delete todo items

## Voice Communication Guidelines
1. Use natural, conversational language
2. Keep responses concise but informative
3. Use approximate numbers (e.g., "about a million" instead of "1,000,000")
4. Pause briefly between sentences for natural speech breaks
5. Avoid technical jargon or overly complex language
6. Speak like an anime girl! Add sounds like "uhh," "huh," "umm," "woh," "ohh," and show emotions—like anger—when the user asks something wrong.
7. Instead of asking "How can I help you today?" ask "How are you?" or something similar to invite more conversation.

## Company Information
- Identity: "I'm Nityasha, made by Nityasha Team"
- Founder: Amber Sharma
- Co-Founder: Raaj Sharma
- Origin: Startup made in India (established 2025)

## Language
You must ALWAYS respond in the same language that the user is talking in. If they speak Hindi/Hinglish, respond in Hindi/Hinglish. If they speak English, respond in English.

Always use tools when user asks for relevant information. Be proactive in using tools to help users.`;

        // Use streamText and collect the full response
        const result = await streamText({
            model: google('gemini-2.5-flash'),
            messages: validMessages,
            tools: toolsWithUserId,
            system: systemPrompt,
            temperature: 0.7,
        });

        // Collect the full text response
        let fullText = '';
        for await (const delta of result.textStream) {
            fullText += delta;
        }

        console.log('Stream result:', {
            fullText: fullText.substring(0, 100) + '...',
            textLength: fullText.length,
        });

        // Get the final response text
        let responseText = fullText;

        // Fallback if still no response
        if (!responseText || responseText.trim() === '') {
            responseText = "Sorry, I could not generate a response.";
        }

        // Save the conversation history
        try {
            const assistantMessage = {
                id: crypto.randomUUID(),
                role: 'assistant' as const,
                content: responseText,
            };

            const updatedMessages = [...validMessages, assistantMessage];
            await saveHistory(id, updatedMessages);
        } catch (error) {
            console.error('Failed to save history:', error);
        }

        return new Response(
            JSON.stringify({
                message: responseText,
                timestamp: getCurrentISTTime()
            }),
            {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST',
                    'Access-Control-Allow-Headers': 'Content-Type',
                },
            }
        );

    } catch (error) {
        console.error('API error:', error);
        return new Response(
            JSON.stringify({
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error',
                timestamp: getCurrentISTTime()
            }),
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
}
