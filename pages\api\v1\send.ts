import { NextApiRequest, NextApiResponse } from 'next';
import { query } from '@/lib/db';
import { OkPacket, RowDataPacket } from 'mysql2';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method === 'POST') {
        const { userId, consultantId, bel, time, RoomN } = req.body;

        if (!userId || !consultantId || !bel || !time || !RoomN) {
            return res.status(400).json({ message: 'Missing required fields' });
        }

        try {
            // Check if RoomN already exists
            const existingChat = await query(
                'SELECT id FROM chatRequests WHERE RoomN = ? LIMIT 1',
                [RoomN]
            ) as RowDataPacket[];

            if (existingChat.length > 0) {
                // Return full existing chat data
                const existingRoom = await query(
                    'SELECT * FROM chatRequests WHERE RoomN = ? LIMIT 1',
                    [RoomN]
                ) as RowDataPacket[];

                return res.status(200).json({
                    message: 'Chat request already exists',
                    chatRequest: existingRoom[0]
                });
            }

            // Insert new chat request
            const result = await query(
                'INSERT INTO chatRequests (userId, consultantId, bel, time, RoomN) VALUES (?, ?, ?, ?, ?)',
                [userId, consultantId, bel, time, RoomN]
            );

            const insertResult = result as OkPacket;
            const chatRequestId = insertResult.insertId || null;

            return res.status(201).json({
                message: 'Chat request successfully created',
                chatRequestId: chatRequestId
            });
        } catch (error) {
            console.error('Error creating chat request:', error);
            return res.status(500).json({ message: 'Internal server error' });
        }
    } else {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
